package com.rs.module.ihc.service.approval;

import cn.hutool.core.map.MapBuilder;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import com.rs.module.ihc.enums.MedicineDeliveryApplyStatusEnum;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Field;

/**
 * @ClassName ApprovalServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/7 14:18
 * @Version 1.0
 */
@Log4j2
public abstract class ApprovalServiceImpl<DAO extends BaseMapper<DO> & IBaseDao<DO>, DO> extends BaseServiceImpl<DAO, DO> {

    @Resource
    private DAO baseDao;

    @Resource
    private BpmApi bpmApi;


    public abstract String getUrl(DO entityDO);

    public abstract String getDefKey();

    public JSONObject commonStartProcess(MapBuilder<String, Object> variables, String id, String msgTitle, DO entity, String status) {
        JSONObject result = BspApprovalUtil.commonStartProcess(getDefKey(), id, msgTitle, getUrl(entity), variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            throw new ServerException("流程启动失败");
        }
        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");

        // 通过反射设置entity字段
        try {
            // 设置actInstId字段
            setFieldValue(entity, "actInstId", bpmTrail.getString("actInstId"));

            // 设置taskId字段
            setFieldValue(entity, "taskId", bpmTrail.getString("taskId"));

            // 设置status字段 - 使用传入的status参数，如果为空则使用默认值
            setFieldValue(entity, "status", status);

            // 更新实体
            baseDao.updateById(entity);

        } catch (Exception e) {
            log.error("通过反射设置entity字段失败", e);
            throw new ServerException("设置流程字段失败");
        }
        return result;
    }

    public void approve(SimpleApproveReqVO approveReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        DO entity = baseDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在！");

        MedicineDeliveryApplyStatusEnum statusEnum = MedicineDeliveryApplyStatusEnum.getByCode(entity.getStatus());
        if (!MedicineDeliveryApplyStatusEnum.DSP.getCode().equals(statusEnum.getCode())) {
            throw new ServerException("该状态[" + statusEnum.getName() + "]不允许审批！");
        }

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        Assert.isTrue(isApproval, "当前人无审批权限" );

        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        String status = MedicineDeliveryApplyStatusEnum.YWC.getCode();
        if (String.valueOf( BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            status = MedicineDeliveryApplyStatusEnum.YZZ.getCode();
        }
        entity.setStatus(status);

        JSONObject result = BspApprovalUtil.approvalProcessAcp(defKey,
                entity.getActInstId(),
                entity.getTaskId(),
                entity.getId(),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments());

        log.info("=======result:{}", result);
        if(result.getIntValue("code") != HttpStatus.OK.value()){
            throw new ServerException("流程审批失败");
        }

        Boolean finishProcinst = bpmApi.isFinishProcinst(entity.getActInstId());
        if(finishProcinst != null && !finishProcinst){
            entity.setStatus(MedicineDeliveryApplyStatusEnum.DSP.getCode());
        }

        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        entity.setTaskId(bpmTrail.getString("taskId"));
        medicineDeliveryApplyDao.updateById(entity);


    }


    /**
     * 通过反射设置对象字段值
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @param value     字段值
     */
    private void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            Class<?> clazz = obj.getClass();
            Field field = null;

            // 查找字段，包括父类中的字段
            while (clazz != null && field == null) {
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
                log.debug("成功设置字段 {} 的值为: {}", fieldName, value);
            } else {
                log.warn("未找到字段: {}", fieldName);
            }
        } catch (Exception e) {
            log.error("设置字段 {} 失败", fieldName, e);
            throw new RuntimeException("设置字段失败: " + fieldName, e);
        }
    }


}
