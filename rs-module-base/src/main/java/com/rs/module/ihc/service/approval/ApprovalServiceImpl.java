package com.rs.module.ihc.service.approval;

import cn.hutool.core.map.MapBuilder;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.enums.MedicineDeliveryApplyStatusEnum;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Field;

/**
 * @ClassName ApprovalServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/7 14:18
 * @Version 1.0
 */
@Log4j2
public abstract class ApprovalServiceImpl<DAO extends BaseMapper<DO> & IBaseDao<DO>, DO> extends BaseServiceImpl<DAO, DO> {

    @Resource
    private DAO baseDao;

    @Resource
    private BpmApi bpmApi;


    public abstract String getUrl(DO entityDO);

    public abstract String getDefKey();

    public abstract String isValidApproveStatus(String status);

    public abstract String getApproveStatus(SimpleApproveReqVO approveReqVO);
    public abstract void finishApproveStatus(SimpleApproveReqVO approveReqVO);


    public JSONObject commonStartProcess(MapBuilder<String, Object> variables, String id, String msgTitle, DO entity, String status) {
        JSONObject result = BspApprovalUtil.commonStartProcess(getDefKey(), id, msgTitle, getUrl(entity), variables.build(), HttpUtils.getAppCode());
        log.info("==========result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            throw new ServerException("流程启动失败");
        }
        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");

        // 通过反射设置entity字段
        try {
            // 设置actInstId字段
            setFieldValue(entity, "actInstId", bpmTrail.getString("actInstId"));

            // 设置taskId字段
            setFieldValue(entity, "taskId", bpmTrail.getString("taskId"));

            // 设置status字段 - 使用传入的status参数，如果为空则使用默认值
            setFieldValue(entity, "status", status);

            // 更新实体
            baseDao.updateById(entity);

        } catch (Exception e) {
            log.error("通过反射设置entity字段失败", e);
            throw new ServerException("设置流程字段失败");
        }
        return result;
    }

    public void approve(SimpleApproveReqVO approveReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        DO entity = baseDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, "记录不存在！");
        String currentStatus = getStatus(entity);
        String errorMsg = isValidApproveStatus(currentStatus);
        if (StringUtils.isNotEmpty(errorMsg)) {
            throw new ServerException(errorMsg);
        }

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(getTaskId(entity), sessionUser.getIdCard());
        Assert.isTrue(isApproval, "当前人无审批权限");

        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf(BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED;
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
        }
        setFieldValue(entity, "status", getApproveStatus(approveReqVO));

        JSONObject result = BspApprovalUtil.approvalProcessAcp(getDefKey(),
                getActInstId(entity),
                getTaskId(entity),
                getId(entity),
                bspApproceStatusEnum,
                approveReqVO.getApprovalComments());

        log.info("=======result:{}", result);
        if (result.getIntValue("code") != HttpStatus.OK.value()) {
            throw new ServerException("流程审批失败");
        }

        Boolean finishProcinst = bpmApi.isFinishProcinst(getActInstId(entity));
        if (finishProcinst != null && !finishProcinst) {
            entity.setStatus(MedicineDeliveryApplyStatusEnum.DSP.getCode());
        }

        JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
        setFieldValue(entity, "status", getApproveStatus(approveReqVO));
        baseDao.updateById(entity);


    }


    /**
     * 通过反射设置对象字段值
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @param value     字段值
     */
    private void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            Class<?> clazz = obj.getClass();
            Field field = null;

            // 查找字段，包括父类中的字段
            while (clazz != null && field == null) {
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
                log.debug("成功设置字段 {} 的值为: {}", fieldName, value);
            } else {
                log.warn("未找到字段: {}", fieldName);
            }
        } catch (Exception e) {
            log.error("设置字段 {} 失败", fieldName, e);
            throw new RuntimeException("设置字段失败: " + fieldName, e);
        }
    }

    /**
     * 通过反射获取对象字段值
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private Object getFieldValue(Object obj, String fieldName) {
        try {
            Class<?> clazz = obj.getClass();
            Field field = null;

            // 查找字段，包括父类中的字段
            while (clazz != null && field == null) {
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                field.setAccessible(true);
                Object value = field.get(obj);
                log.debug("成功获取字段 {} 的值: {}", fieldName, value);
                return value;
            } else {
                log.warn("未找到字段: {}", fieldName);
                return null;
            }
        } catch (Exception e) {
            log.error("获取字段 {} 失败", fieldName, e);
            throw new RuntimeException("获取字段失败: " + fieldName, e);
        }
    }

    /**
     * 通过反射获取对象字段值（泛型版本）
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @param clazz     期望的返回类型
     * @return 字段值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFieldValue(Object obj, String fieldName, Class<T> clazz) {
        Object value = getFieldValue(obj, fieldName);
        if (value == null) {
            return null;
        }

        try {
            return (T) value;
        } catch (ClassCastException e) {
            log.error("字段 {} 的值类型转换失败，期望类型: {}，实际类型: {}",
                    fieldName, clazz.getSimpleName(), value.getClass().getSimpleName());
            throw new RuntimeException("字段类型转换失败: " + fieldName, e);
        }
    }

    /**
     * 通过反射获取实体的流程实例ID
     *
     * @param entity 实体对象
     * @return 流程实例ID
     */
    protected String getActInstId(DO entity) {
        return getFieldValue(entity, "actInstId", String.class);
    }

    protected String getId(DO entity) {
        return getFieldValue(entity, "id", String.class);
    }

    /**
     * 通过反射获取实体的任务ID
     *
     * @param entity 实体对象
     * @return 任务ID
     */
    protected String getTaskId(DO entity) {
        return getFieldValue(entity, "taskId", String.class);
    }

    /**
     * 通过反射获取实体的状态
     *
     * @param entity 实体对象
     * @return 状态值
     */
    protected String getStatus(DO entity) {
        return getFieldValue(entity, "status", String.class);
    }

    /**
     * 通过反射批量获取实体的流程相关字段
     *
     * @param entity 实体对象
     * @return 包含actInstId、taskId、status的对象
     */
    protected ProcessFieldsInfo getProcessFields(DO entity) {
        String actInstId = getActInstId(entity);
        String taskId = getTaskId(entity);
        String status = getStatus(entity);

        return new ProcessFieldsInfo(actInstId, taskId, status);
    }

    /**
     * 流程字段信息封装类
     */
    protected static class ProcessFieldsInfo {
        private final String actInstId;
        private final String taskId;
        private final String status;

        public ProcessFieldsInfo(String actInstId, String taskId, String status) {
            this.actInstId = actInstId;
            this.taskId = taskId;
            this.status = status;
        }

        public String getActInstId() {
            return actInstId;
        }

        public String getTaskId() {
            return taskId;
        }

        public String getStatus() {
            return status;
        }

        @Override
        public String toString() {
            return "ProcessFieldsInfo{" +
                    "actInstId='" + actInstId + '\'' +
                    ", taskId='" + taskId + '\'' +
                    ", status='" + status + '\'' +
                    '}';
        }
    }


}
